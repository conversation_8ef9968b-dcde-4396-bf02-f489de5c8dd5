<view class="marathon-container">
  <!-- 使用系统默认导航栏 -->

  <!-- 内容滚动区域 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">
    
    <!-- 赛事英雄区域 - 增强版 -->
    <view class="event-hero-section">
      <view class="hero-image-container">
        <image class="hero-image" src="{{eventInfo.banner}}" mode="aspectFill" binderror="onImageError" bindload="onImageLoad" />
        <view class="hero-overlay-gradient"></view>
        <view class="hero-particles">
          <view class="particle particle-1"></view>
          <view class="particle particle-2"></view>
          <view class="particle particle-3"></view>
          <view class="particle particle-4"></view>
          <view class="particle particle-5"></view>
        </view>
        
        <!-- 状态标签 - 增强动画 -->
        <view class="status-badge {{eventInfo.status}} animated-badge">
          <view class="status-icon pulse-animation"></view>
          <text wx:if="{{eventInfo.status === 'registering'}}">报名中</text>
          <text wx:elif="{{eventInfo.status === 'upcoming'}}">即将开始</text>
          <text wx:else>已截止</text>
        </view>
        
        <!-- 热门标签 - 火焰动画 -->
        <view class="hot-badge flame-animation" wx:if="{{eventInfo.isHot}}">
          <van-icon name="fire-o" size="12px" color="#fff" />
          <text>热门</text>
          <view class="flame-effect"></view>
        </view>
        
        <!-- 倒计时标签 - 跳动动画 -->
        <view class="countdown-badge bounce-animation" wx:if="{{eventInfo.countdownDays > 0}}">
          <van-icon name="clock-o" size="12px" color="#fff" />
          <text>{{eventInfo.countdownDays}}天</text>
          <view class="countdown-ring"></view>
        </view>
        
        <!-- 赛事基本信息 - 渐入动画 -->
        <view class="hero-content fade-in-up">
          <text class="hero-title gradient-text">{{eventInfo.name || '2024北京国际马拉松'}}</text>
          <view class="hero-meta">
            <view class="meta-item slide-in-left">
              <view class="meta-icon-wrapper">
                <van-icon name="location-o" size="14px" color="rgba(255,255,255,0.9)" />
              </view>
              <text>{{eventInfo.location || '北京天安门广场'}}</text>
            </view>
            <view class="meta-item slide-in-right">
              <view class="meta-icon-wrapper">
                <van-icon name="clock-o" size="14px" color="rgba(255,255,255,0.9)" />
              </view>
              <text>{{eventInfo.date || '2024年10月15日 08:00'}}</text>
            </view>
          </view>
          

        </view>
      </view>
    </view>

    <!-- 快速信息卡片 - 3D效果 -->
    <view class="quick-info-section">
      <view class="info-cards">
        <view class="info-card card-3d hover-lift" data-index="0">
          <view class="card-background-effect"></view>
          <view class="card-icon icon-bounce">
            <van-icon name="friends-o" size="20px" color="#a5191e" />
          </view>
          <view class="card-content">
            <text class="card-number counter-animation">{{eventInfo.participants || 0}}</text>
            <text class="card-label">已报名</text>
          </view>
          <view class="card-shine"></view>
        </view>
        
        <view class="info-card card-3d hover-lift" data-index="1">
          <view class="card-background-effect"></view>
          <view class="card-icon icon-bounce">
            <van-icon name="location-o" size="20px" color="#ff8f1f" />
          </view>
          <view class="card-content">
            <text class="card-number counter-animation">{{eventInfo.distance || '42.195'}}</text>
            <text class="card-label">公里</text>
          </view>
          <view class="card-shine"></view>
        </view>
        
        <view class="info-card card-3d hover-lift" data-index="2">
          <view class="card-background-effect"></view>
          <view class="card-icon icon-bounce">
            <van-icon name="star-o" size="20px" color="#25b864" />
          </view>
          <view class="card-content">
            <text class="card-number counter-animation">{{eventInfo.rating || '4.8'}}</text>
            <text class="card-label">评分</text>
          </view>
          <view class="card-shine"></view>
        </view>
      </view>
    </view>

    <!-- 赛事描述 - 渐入效果 -->
    <view class="content-section fade-in-section" wx:if="{{eventInfo.description}}">
      <view class="section-header modern-header">
        <view class="section-icon icon-glow">
          <van-icon name="info-o" size="18px" color="#a5191e" />
        </view>
        <text class="section-title">赛事介绍</text>
        <view class="title-underline"></view>
      </view>
      <view class="section-content glass-effect">
        <rich-text class="description-text typewriter-effect" nodes="{{eventInfo.description}}"></rich-text>
      </view>
    </view>

    <!-- 赛事亮点 - 卡片动画 -->
    <view class="content-section fade-in-section" wx:if="{{eventInfo.highlights && eventInfo.highlights.length > 0}}">
      <view class="section-header modern-header">
        <view class="section-icon icon-glow">
          <van-icon name="star-o" size="18px" color="#ff8f1f" />
        </view>
        <text class="section-title">赛事亮点</text>
        <view class="title-underline"></view>
      </view>
      <view class="highlights-grid">
        <view class="highlight-card floating-card" wx:for="{{eventInfo.highlights}}" wx:key="index">
          <view class="highlight-icon sparkle-animation">✨</view>
          <view class="highlight-content">
            <text class="highlight-title">{{item.title}}</text>
            <text class="highlight-value">{{item.value}}</text>
          </view>
          <view class="card-ripple"></view>
        </view>
      </view>
    </view>



    <!-- 报名步骤 - 进度动画 -->
    <view class="steps-container modern-steps">
      <view class="steps-background"></view>
      <view class="step {{currentStep >= 1 ? 'active' : ''}} step-animation" data-step="1">
        <view class="step-number">
          <text>1</text>
          <view class="step-ripple"></view>
        </view>
        <view class="step-text">选择项目</view>
      </view>
      <view class="step-line progress-line {{currentStep >= 2 ? 'completed' : ''}}"></view>
      <view class="step {{currentStep >= 2 ? 'active' : ''}} step-animation" data-step="2">
        <view class="step-number">
          <text>2</text>
          <view class="step-ripple"></view>
        </view>
        <view class="step-text">填写信息</view>
      </view>
      <view class="step-line progress-line {{currentStep >= 3 ? 'completed' : ''}}"></view>
      <view class="step {{currentStep >= 3 ? 'active' : ''}} step-animation" data-step="3">
        <view class="step-number">
          <text>3</text>
          <view class="step-ripple"></view>
        </view>
        <view class="step-text">确认支付</view>
      </view>
      <view class="step-line progress-line {{currentStep >= 4 ? 'completed' : ''}}"></view>
      <view class="step {{currentStep >= 4 ? 'active' : ''}} step-animation" data-step="4">
        <view class="step-number">
          <text>4</text>
          <view class="step-ripple"></view>
        </view>
        <view class="step-text">报名成功</view>
      </view>
    </view>

    

    <!-- 报名项目选择 - 交互式卡片 -->
    <view class="section-container modern-section">
      <view class="section-title modern-title">
        <view class="title-icon gradient-icon"></view>
        <text>选择比赛项目</text>
        <view class="title-decoration"></view>
      </view>

      <view class="race-options modern-options">
        <block wx:for="{{raceOptions}}" wx:for-item="race" wx:for-index="raceKey" wx:key="raceKey">
          <view class="race-option interactive-card {{selectedRace === raceKey ? 'selected' : ''}}" bindtap="selectRace" data-race="{{raceKey}}">
            <view class="option-background"></view>
            <view class="race-option-content">
              <view class="race-name">{{race.name}}</view>
              <view class="race-details">
                <text class="race-distance">{{race.distance}}公里</text>
                <text class="race-price price-highlight">¥{{race.price}}</text>
              </view>
            </view>
            <view class="race-option-check">
              <van-icon name="{{selectedRace === raceKey ? 'checked' : 'circle'}}" color="{{selectedRace === raceKey ? '#a5191e' : '#ddd'}}" size="20px" />
            </view>
            <view class="selection-glow"></view>
          </view>
        </block>
      </view>
    </view>

    <!-- 个人信息填写 - 现代表单 -->
    <view class="section-container modern-section">
      <view class="section-title modern-title">
        <view class="title-icon gradient-icon"></view>
        <text>个人信息</text>
        <view class="quick-fill modern-quick-fill" bindtap="fillFromProfile">
          <van-icon name="contact" size="14px" color="#a5191e" />
          <text>使用个人资料</text>
          <view class="quick-fill-glow"></view>
        </view>
        <view class="title-decoration"></view>
      </view>

      <view class="form-group modern-form">
        <view class="form-item modern-input">
          <text class="form-label required">姓名</text>
          <input class="form-input" value="{{formData.name}}" bindinput="handleInput" data-field="name" placeholder="请输入真实姓名" />
          <view class="input-underline"></view>
        </view>

        <view class="form-item modern-input">
          <text class="form-label required">身份证号</text>
          <input class="form-input" value="{{formData.idCard}}" bindinput="handleInput" data-field="idCard" placeholder="请输入18位身份证号码" type="idcard" />
          <view class="input-underline"></view>
        </view>

        <view class="form-item modern-input">
          <text class="form-label required">手机号码</text>
          <input class="form-input" value="{{formData.phone}}" bindinput="handleInput" data-field="phone" placeholder="请输入手机号码" type="number" />
          <view class="input-underline"></view>
        </view>

        <view class="form-item modern-radio">
          <text class="form-label required">性别</text>
          <view class="form-radio-group">
            <view class="form-radio modern-radio-item {{formData.gender === 'male' ? 'selected' : ''}}" bindtap="selectGender" data-gender="male">
              <van-icon name="{{formData.gender === 'male' ? 'checked' : 'circle'}}" color="{{formData.gender === 'male' ? '#a5191e' : '#ddd'}}" size="18px" />
              <text>男</text>
              <view class="radio-ripple"></view>
            </view>
            <view class="form-radio modern-radio-item {{formData.gender === 'female' ? 'selected' : ''}}" bindtap="selectGender" data-gender="female">
              <van-icon name="{{formData.gender === 'female' ? 'checked' : 'circle'}}" color="{{formData.gender === 'female' ? '#a5191e' : '#ddd'}}" size="18px" />
              <text>女</text>
              <view class="radio-ripple"></view>
            </view>
          </view>
        </view>

        <view class="form-item modern-input">
          <text class="form-label required">出生日期</text>
          <view class="form-picker" bindtap="showDatePicker">
            <text>{{formData.birthdate || '请选择出生日期'}}</text>
            <van-icon name="arrow-down" size="14px" color="#999" />
          </view>
          <view class="picker-underline"></view>
        </view>

        <!-- Vant 日期选择器弹窗 -->
        <van-popup show="{{showDatePicker}}" position="bottom" bind:close="hideDatePicker">
          <van-datetime-picker
            type="date"
            value="{{datePickerValue}}"
            min-date="{{minDate}}"
            max-date="{{maxDate}}"
            bind:confirm="onDateConfirm"
            bind:cancel="hideDatePicker"
            title="选择出生日期"
          />
        </van-popup>

        <view class="form-item modern-input">
          <text class="form-label required">紧急联系人</text>
          <input class="form-input" value="{{formData.emergencyContact}}" bindinput="handleInput" data-field="emergencyContact" placeholder="请输入紧急联系人姓名" />
          <view class="input-underline"></view>
        </view>

        <view class="form-item modern-input">
          <text class="form-label required">紧急联系电话</text>
          <input class="form-input" value="{{formData.emergencyPhone}}" bindinput="handleInput" data-field="emergencyPhone" placeholder="请输入紧急联系人电话" type="number" />
          <view class="input-underline"></view>
        </view>

        <view class="form-item modern-input">
          <text class="form-label">最好成绩</text>
          <input class="form-input" value="{{formData.bestRecord}}" bindinput="handleInput" data-field="bestRecord" placeholder="格式：3小时45分钟（选填）" />
          <view class="input-underline"></view>
        </view>

        <view class="form-item modern-picker">
          <text class="form-label">T恤尺码</text>
          <picker mode="selector" range="{{tShirtSizes}}" value="{{tShirtSizeIndex}}" bindchange="bindTShirtChange">
            <view class="form-picker">
              <text>{{tShirtSizes[tShirtSizeIndex] || '请选择尺码'}}</text>
              <van-icon name="arrow-down" size="14px" color="#999" />
            </view>
          </picker>
          <view class="picker-underline"></view>
        </view>
      </view>
    </view>

    <!-- 增值服务 - 服务卡片 -->
    <view class="section-container modern-section">
      <view class="section-title modern-title">
        <view class="title-icon gradient-icon"></view>
        <text>增值服务</text>
        <view class="title-decoration"></view>
      </view>

      <view class="add-on-services modern-services">
        <!-- 动态显示增值服务 -->
        <block wx:for="{{availableAddOns}}" wx:key="key">
          <view class="add-on-item service-card {{addOns[item.key] ? 'selected' : ''}}" bindtap="toggleAddOn" data-addon="{{item.key}}">
            <view class="service-background"></view>
            <view class="add-on-check">
              <van-icon name="{{addOns[item.key] ? 'checked' : 'circle'}}" color="{{addOns[item.key] ? '#a5191e' : '#ddd'}}" size="18px" />
            </view>
            <view class="add-on-content">
              <view class="add-on-title">
                <text>{{item.name}}</text>
              </view>
              <text class="add-on-desc">{{item.description}}</text>
            </view>
            <view class="service-glow"></view>
          </view>
        </block>
      </view>
    </view>

    <!-- 健康声明 - 现代化声明 -->
    <view class="section-container modern-section">
      <view class="section-title modern-title">
        <view class="title-icon gradient-icon"></view>
        <text>健康声明</text>
        <view class="title-decoration"></view>
      </view>

      <view class="health-declaration modern-declaration">
        <view class="declaration-item declaration-card {{healthDeclaration.noDisease ? 'selected' : ''}}" bindtap="toggleDeclaration" data-declaration="noDisease">
          <van-icon name="{{healthDeclaration.noDisease ? 'checked' : 'circle'}}" color="{{healthDeclaration.noDisease ? '#a5191e' : '#ddd'}}" size="18px" />
          <text>我声明本人无心脏病、高血压等不适合参加马拉松的疾病</text>
          <view class="declaration-ripple"></view>
        </view>

        <view class="declaration-item declaration-card {{healthDeclaration.fitForRace ? 'selected' : ''}}" bindtap="toggleDeclaration" data-declaration="fitForRace">
          <van-icon name="{{healthDeclaration.fitForRace ? 'checked' : 'circle'}}" color="{{healthDeclaration.fitForRace ? '#a5191e' : '#ddd'}}" size="18px" />
          <text>我确认身体状况良好，适合参加马拉松比赛</text>
          <view class="declaration-ripple"></view>
        </view>

        <view class="declaration-item declaration-card {{healthDeclaration.followRules ? 'selected' : ''}}" bindtap="toggleDeclaration" data-declaration="followRules">
          <van-icon name="{{healthDeclaration.followRules ? 'checked' : 'circle'}}" color="{{healthDeclaration.followRules ? '#a5191e' : '#ddd'}}" size="18px" />
          <text>我承诺遵守赛事规则，服从赛事工作人员指挥</text>
          <view class="declaration-ripple"></view>
        </view>
      </view>
    </view>

    <!-- 报名协议 - 现代化协议 -->
    <view class="agreement modern-agreement">
      <view class="agreement-check modern-check {{agreementChecked ? 'checked' : ''}}" bindtap="toggleAgreement">
        <van-icon name="{{agreementChecked ? 'checked' : 'circle'}}" color="{{agreementChecked ? '#a5191e' : '#ddd'}}" size="18px" />
        <view class="check-ripple"></view>
      </view>
      <view class="agreement-text">
        <text>我已阅读并同意</text>
        <text class="agreement-link" catchtap="showAgreement">《马拉松赛事报名协议》</text>
      </view>
    </view>



    <!-- 费用明细 - 现代化明细 -->
    <view class="fee-summary modern-fee-summary">
      <view class="fee-background"></view>
      <view class="fee-item">
        <text>报名费用</text>
        <text>¥{{fees.registration || 0}}</text>
      </view>
      <view class="fee-item" wx:if="{{fees.addOns > 0}}">
        <text>增值服务</text>
        <text>¥{{fees.addOns || 0}}</text>
      </view>
      <view class="fee-divider modern-divider"></view>
      <view class="fee-total">
        <text>总计</text>
        <text class="total-amount gradient-price">¥{{fees.total || 0}}</text>
      </view>
    </view>

    <!-- 底部操作栏 - 现代化按钮 -->
    <view class="bottom-bar modern-bottom-bar">
      <view class="bottom-background"></view>
      <view class="price-info">
        <text>总计：</text>
        <text class="price gradient-price">¥{{fees.total || 0}}</text>
      </view>
      <button class="submit-btn premium-submit-btn {{canSubmit ? 'active' : 'disabled'}}" bindtap="submitRegistration" disabled="{{!canSubmit}}">
        <view class="btn-background-effect"></view>
        <view class="btn-content">
          <view class="btn-icon">
            <van-icon name="play" size="16px" color="#fff" />
          </view>
          <text class="btn-text">立即报名</text>
          <text class="btn-price">{{fees.total > 0 ? '¥' + fees.total : '免费'}}</text>
        </view>
        <view class="btn-shine"></view>
        <view class="btn-particles">
          <view class="particle"></view>
          <view class="particle"></view>
          <view class="particle"></view>
        </view>
      </button>
    </view>

    <!-- 支付弹窗 - 现代化弹窗 -->
    <van-popup show="{{showPaymentPopup}}" round position="bottom" custom-style="height: 40%;" bind:close="closePaymentPopup">
      <view class="payment-popup modern-payment-popup">
        <view class="payment-header">
          <text class="payment-title">确认支付</text>
          <van-icon name="cross" size="22px" color="#999" bindtap="closePaymentPopup" />
        </view>

        <view class="payment-amount">
          <text class="amount-text gradient-price">¥{{fees.total || 0}}</text>
        </view>

        <view class="payment-methods">
          <view class="payment-method modern-payment-method {{paymentMethod === 'wechat' ? 'selected' : ''}}" bindtap="selectPaymentMethod" data-method="wechat">
            <view class="method-icon wechat">
              <van-icon name="wechat-pay" size="24px" color="#09bb07" />
            </view>
            <view class="method-info">
              <text class="method-name">微信支付</text>
              <text class="method-desc">微信安全支付</text>
            </view>
            <van-icon name="{{paymentMethod === 'wechat' ? 'checked' : 'circle'}}" color="{{paymentMethod === 'wechat' ? '#a5191e' : '#ddd'}}" size="20px" />
            <view class="method-glow"></view>
          </view>

          <!-- 余额支付选项已移除 -->
        </view>

        <button class="confirm-payment-btn modern-confirm-btn" bindtap="confirmPayment">
          <text>确认支付</text>
          <view class="confirm-btn-glow"></view>
        </button>
      </view>
    </van-popup>
  </scroll-view>
</view>
