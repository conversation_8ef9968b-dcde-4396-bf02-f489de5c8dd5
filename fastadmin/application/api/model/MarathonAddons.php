<?php

namespace app\api\model;

use think\Model;

/**
 * 马拉松增值服务模型
 */
class MarathonAddons extends Model
{
    // 表名
    protected $name = 'addons';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 字段类型转换
    protected $type = [
        'price' => 'float',
        'status' => 'boolean'
    ];
    
    /**
     * 关联赛事
     */
    public function marathonEvent()
    {
        return $this->belongsTo('Events', 'event_id');
    }
}