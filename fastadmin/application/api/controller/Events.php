<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Events as EventsModel;
use app\api\model\EventsRegistrations;
use app\api\model\EventsPayments;
use app\api\model\EventsRaceOptions;
use think\Db;
use think\Exception;

/**
 * 赛事接口
 */
class Events extends Api
{
    protected $noNeedLogin = ['list', 'detail', 'race_options'];
    protected $noNeedRight = '*';
    
    /**
     * 获取赛事列表
     *
     * @ApiTitle    (获取赛事列表)
     * @ApiSummary  (获取赛事列表，支持分页和筛选)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/events/list)
     * @ApiParams   ({"name":"page","type":"integer","required":false,"description":"页码 (默认1)"})
     * @ApiParams   ({"name":"limit","type":"integer","required":false,"description":"每页数量 (默认10)"})
     * @ApiParams   ({"name":"status","type":"string","required":false,"description":"状态筛选"})
     * @ApiParams   ({"name":"location","type":"string","required":false,"description":"地点筛选"})
     * @ApiReturn   ({"code":0,"msg":"获取成功","data":{"total":100,"list":[{"id":1,"name":"2024北京马拉松","date":"2024-10-15","location":"北京市","banner":"/uploads/events/banner1.jpg","status":"normal"}]}})
     */
    public function list()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $status = $this->request->param('status', '');
        $location = $this->request->param('location', '');
        
        $where = [];
        if ($status && $status !== 'all') {
            $where['status'] = $status;
        } elseif (!$status) {
            $where['status'] = 'normal';
        }
        
        if ($location) {
            $where['location'] = ['like', '%' . $location . '%'];
        }
        
        $events = EventsModel::where($where)
                            ->order('event_date desc')
                            ->paginate($limit, false, ['page' => $page]);
        
        $result = [
            'total' => $events->total(),
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => $events->lastPage(),
            'data' => []
        ];
        
        foreach ($events as $event) {
            $result['data'][] = [
                'id' => $event->id,
                'name' => $event->name,
                'date' => $event->event_date ?: '0000-00-00',
                'location' => $event->location,
                'coverImage' => $event->image ?: '',
                'price' => $event->price ?: 100,
                'status' => $event->status,
                'countdownDays' => $event->countdown_days ?: 0,
                'participants' => $event->participants ?: 30000,
                'type' => $event->type ?: 'full'
            ];
        }
        
        $this->success('success', $result);
    }
    
    /**
     * 获取赛事详情
     *
     * @ApiTitle    (获取赛事详情)
     * @ApiSummary  (根据ID获取赛事详细信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/events/detail)
     * @ApiParams   ({"name":"id","type":"integer","required":true,"description":"赛事ID"})
     * @ApiReturn   ({"code":0,"msg":"获取成功","data":{"id":1,"name":"2024北京马拉松","date":"2024-10-15","location":"北京市","banner":"/uploads/events/banner1.jpg","status":"normal","countdownDays":30,"description":"赛事描述","highlights":["专业计时","完赛奖牌"],"gallery":["/uploads/events/gallery1.jpg"],"raceOptions":[{"id":1,"name":"全程马拉松","distance":"42.195km","price":200}],"addons":[{"id":1,"name":"赛事保险","price":20,"description":"赛事意外保险"}]}})
     */
    public function detail()
    {
        $id = $this->request->param('id');
        if (!$id) {
            $this->error('缺少赛事ID参数');
        }
        
        $event = EventsModel::where('id', $id)->where('status', 'normal')->find();
        if (!$event) {
            $this->error('赛事不存在');
        }
        
        // 先获取处理过的赛事选项
        $raceOptions = $this->getRaceOptions($event->id);

        // 从处理过的赛事选项中计算最低价
        $prices = array_column($raceOptions, 'price');
        $startingPrice = !empty($prices) ? min(array_filter($prices)) : 0;

        // 构建赛事详情数据
        $result = [
            'id' => $event->id,
            'name' => 'FIXED: ' . $event->name,
            'date' => $event->event_date,
            'location' => $event->location,
            'banner' => $event->image,
            'status' => $event->status,
            'is_hot' => $event->is_hot ? true : false,
            'participants' => $this->getTotalParticipants($event->id),
            'distance' => (string)($event->distance ?: '42.195'),
            'rating' => $event->rating ?: 5.0,
            'description' => $event->description,
            'price' => $startingPrice, // 使用修正后的最低价
            'registration_start' => $event->registration_start,
            'registration_end' => $event->registration_end,
            'countdownDays' => $this->getCountdownDays($event->event_date),
            'isFavorite' => false, // TODO: 需要根据用户登录状态和收藏记录判断
            'gallery' => $this->getEventGallery($event->id),
            'highlights' => $this->getEventHighlights($event),
            'race_options' => $raceOptions, // 使用已获取的赛事选项
            'addons' => $this->getEventAddons($event->id)
        ];
        
        $this->success('获取成功', $result);
    }
    
    /**
     * 赛事报名
     *
     * @ApiTitle    (赛事报名)
     * @ApiSummary  (提交赛事报名信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/events/register)
     * @ApiParams   ({"name":"event_id","type":"integer","required":true,"description":"赛事ID"})
     * @ApiParams   ({"name":"race_option_id","type":"integer","required":true,"description":"赛事项目ID"})
     * @ApiParams   ({"name":"name","type":"string","required":true,"description":"参赛者姓名"})
     * @ApiParams   ({"name":"gender","type":"string","required":true,"description":"性别 (male/female)"})
     * @ApiParams   ({"name":"id_card","type":"string","required":true,"description":"身份证号"})
     * @ApiParams   ({"name":"phone","type":"string","required":true,"description":"手机号码"})
     * @ApiParams   ({"name":"email","type":"string","required":false,"description":"邮箱地址"})
     * @ApiParams   ({"name":"birth_date","type":"string","required":true,"description":"出生日期 (YYYY-MM-DD)"})
     * @ApiParams   ({"name":"nationality","type":"string","required":true,"description":"国籍"})
     * @ApiParams   ({"name":"emergency_contact","type":"string","required":true,"description":"紧急联系人"})
     * @ApiParams   ({"name":"emergency_phone","type":"string","required":true,"description":"紧急联系电话"})
     * @ApiParams   ({"name":"t_shirt_size","type":"string","required":true,"description":"T恤尺码 (XS/S/M/L/XL/XXL)"})
     * @ApiParams   ({"name":"has_insurance","type":"integer","required":false,"description":"是否购买保险 (0=否, 1=是)"})
     * @ApiParams   ({"name":"agree_terms","type":"integer","required":true,"description":"是否同意条款 (1=同意)"})
     * @ApiReturn   ({"code":0,"msg":"报名信息提交成功","data":{"registration_id":12345,"order_no":"REG202412250001","total_fee":200.00,"payment_url":"/api/payment/pay?order_no=REG202412250001"}})
     */
    public function register()
    {
        $params = $this->request->param();
        
        // 验证必填参数
        $required = ['event_id', 'race_option_id', 'name', 'gender', 'id_card', 'phone', 
                    'birth_date', 'nationality', 'emergency_contact', 'emergency_phone', 
                    't_shirt_size', 'agree_terms'];
        
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '') {
                $this->error('缺少必填参数: ' . $field);
            }
        }
        
        // 验证同意条款
        if ($params['agree_terms'] != 1) {
            $this->error('请先同意报名条款');
        }
        
        // 验证赛事是否存在
        $event = EventsModel::where('id', $params['event_id'])
                           ->where('status', 'normal')
                           ->find();
        if (!$event) {
            $this->error('赛事不存在或已下线');
        }
        
        // 验证赛事项目是否存在
        $raceOption = EventsRaceOptions::where('id', $params['race_option_id'])
                                      ->where('event_id', $params['event_id'])
                                      ->where('status', 'normal')
                                      ->find();
        if (!$raceOption) {
            $this->error('赛事项目不存在');
        }
        
        // 检查报名时间
        $now = time();
        if ($event->registration_start && strtotime($event->registration_start) > $now) {
            $this->error('报名尚未开始');
        }
        if ($event->registration_end && strtotime($event->registration_end) < $now) {
            $this->error('报名已截止');
        }
        
        // 检查名额
        if (!$raceOption->hasQuota()) {
            $this->error('该项目名额已满');
        }
        
        // 检查是否重复报名（同一用户同一赛事）
        $userId = $this->auth->id ?? 0; // 假设有用户认证
        if ($userId > 0) {
            $existRegistration = EventsRegistrations::where('user_id', $userId)
                                                   ->where('event_id', $params['event_id'])
                                                   ->where('status', 'in', ['pending', 'paid'])
                                                   ->find();
            if ($existRegistration) {
                $this->error('您已报名该赛事，请勿重复报名');
            }
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 生成订单号
            $orderNo = EventsRegistrations::generateOrderNo();
            
            // 计算总费用
            $totalFee = $raceOption->price;
            if (isset($params['has_insurance']) && $params['has_insurance'] == 1) {
                $totalFee += 20; // 保险费用
            }
            
            // 创建报名记录
            $registration = new EventsRegistrations();
            $registration->user_id = $userId;
            $registration->event_id = $params['event_id'];
            $registration->race_option_id = $params['race_option_id'];
            $registration->order_no = $orderNo;
            $registration->name = $params['name'];
            $registration->gender = $params['gender'];
            $registration->id_card = $params['id_card'];
            $registration->phone = $params['phone'];
            $registration->email = $params['email'] ?? '';
            $registration->birth_date = $params['birth_date'];
            $registration->nationality = $params['nationality'];
            $registration->emergency_contact = $params['emergency_contact'];
            $registration->emergency_phone = $params['emergency_phone'];
            $registration->t_shirt_size = $params['t_shirt_size'];
            $registration->has_insurance = $params['has_insurance'] ?? 0;
            $registration->total_fee = $totalFee;
            $registration->status = EventsRegistrations::STATUS_PENDING;
            
            if (!$registration->save()) {
                throw new Exception('报名信息保存失败');
            }
            
            // 提交事务
            Db::commit();
            
            $this->success('报名信息提交成功', [
                'registration_id' => $registration->id,
                'order_no' => $orderNo,
                'total_fee' => $totalFee,
                'payment_url' => '/api/payment/pay?order_no=' . $orderNo
            ]);
            
        } catch (Exception $e) {
            Db::rollback();
            $this->error('报名失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户报名列表
     *
     * @ApiTitle    (获取用户报名列表)
     * @ApiSummary  (获取当前用户的报名记录列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/events/my_registrations)
     * @ApiParams   ({"name":"user_id","type":"integer","required":true,"description":"用户ID"})
     * @ApiParams   ({"name":"page","type":"integer","required":false,"description":"页码 (默认1)"})
     * @ApiParams   ({"name":"limit","type":"integer","required":false,"description":"每页数量 (默认10)"})
     * @ApiParams   ({"name":"status","type":"string","required":false,"description":"状态筛选 (pending/paid/cancelled)"})
     * @ApiReturn   ({"code":0,"msg":"获取成功","data":{"total":3,"list":[{"id":12345,"event_name":"2024北京马拉松","race_option_name":"全程马拉松","event_date":"2024-10-15","location":"北京市","status":"paid","registration_time":"2024-09-15 10:30:00","total_fee":200.00}]}})
     */
    public function my_registrations()
    {
        $userId = $this->request->param('user_id');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $status = $this->request->param('status', '');
        
        if (!$userId) {
            $this->error('缺少用户ID参数');
        }
        
        $where = ['user_id' => $userId];
        if ($status) {
            $where['status'] = $status;
        }
        
        $registrations = EventsRegistrations::where($where)
                                           ->with(['event', 'raceOption'])
                                           ->order('createtime desc')
                                           ->paginate($limit, false, ['page' => $page]);
        
        $result = [
            'total' => $registrations->total(),
            'list' => []
        ];
        
        foreach ($registrations as $registration) {
            $result['list'][] = [
                'id' => $registration->id,
                'event_name' => $registration->event->name,
                'race_option_name' => $registration->raceOption->name,
                'event_date' => $registration->event->event_date,
                'location' => $registration->event->location,
                'status' => $registration->status,
                'registration_time' => date('Y-m-d H:i:s', $registration->createtime),
                'total_fee' => $registration->total_fee
            ];
        }
        
        $this->success('获取成功', $result);
    }
    
    /**
     * 获取报名详情
     *
     * @ApiTitle    (获取报名详情)
     * @ApiSummary  (根据报名ID获取详细信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/events/registration_detail)
     * @ApiParams   ({"name":"id","type":"integer","required":true,"description":"报名记录ID"})
     * @ApiReturn   ({"code":0,"msg":"获取成功","data":{"id":12345,"order_no":"REG202412250001","event_info":{"name":"2024北京马拉松","date":"2024-10-15","location":"北京市"},"participant_info":{"name":"张三","gender":"male","phone":"13800138000"},"payment_info":{"amount":200.00,"status":"paid"},"status":"paid","qr_code":"data:image/png;base64,..."}})
     */
    public function registration_detail()
    {
        $id = $this->request->param('id');
        
        if (!$id) {
            $this->error('缺少报名ID参数');
        }
        
        $registration = EventsRegistrations::with(['event', 'raceOption', 'payment'])->find($id);
        if (!$registration) {
            $this->error('报名记录不存在');
        }
        
        $result = [
            'id' => $registration->id,
            'order_no' => $registration->order_no,
            'event_info' => [
                'name' => $registration->event->name,
                'date' => $registration->event->event_date,
                'location' => $registration->event->location,
                'race_option' => $registration->raceOption->name
            ],
            'participant_info' => [
                'name' => $registration->name,
                'gender' => $registration->gender,
                'id_card' => $registration->id_card,
                'phone' => $registration->phone,
                'email' => $registration->email,
                'birth_date' => $registration->birth_date,
                'nationality' => $registration->nationality,
                'emergency_contact' => $registration->emergency_contact,
                'emergency_phone' => $registration->emergency_phone,
                't_shirt_size' => $registration->t_shirt_size,
                'has_insurance' => $registration->has_insurance
            ],
            'payment_info' => [
                'amount' => $registration->total_fee,
                'status' => $registration->payment ? $registration->payment->status : 'pending',
                'payment_time' => $registration->payment && $registration->payment->paid_time ? 
                                date('Y-m-d H:i:s', $registration->payment->paid_time) : ''
            ],
            'status' => $registration->status
        ];
        
        // 如果已支付，生成参赛二维码
        if ($registration->status === EventsRegistrations::STATUS_PAID) {
            $result['qr_code'] = $this->generateQRCode($registration->order_no);
        }
        
        $this->success('获取成功', $result);
    }
    
    /**
     * 取消报名
     *
     * @ApiTitle    (取消报名)
     * @ApiSummary  (取消指定的报名记录)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/events/cancel_registration)
     * @ApiParams   ({"name":"registration_id","type":"integer","required":true,"description":"报名记录ID"})
     * @ApiParams   ({"name":"reason","type":"string","required":false,"description":"取消原因"})
     * @ApiReturn   ({"code":0,"msg":"取消成功","data":{"refund_amount":200.00,"refund_time":"3-5个工作日"}})
     */
    public function cancel_registration()
    {
        $registrationId = $this->request->post('registration_id');
        $reason = $this->request->post('reason', '');
        
        if (!$registrationId) {
            $this->error('缺少报名ID参数');
        }
        
        $registration = EventsRegistrations::with(['event', 'payment'])->find($registrationId);
        if (!$registration) {
            $this->error('报名记录不存在');
        }
        
        if (!$registration->canCancel()) {
            $this->error('该报名无法取消');
        }
        
        Db::startTrans();
        try {
            // 更新报名状态
            $registration->status = EventsRegistrations::STATUS_CANCELLED;
            $registration->save();
            
            // 如果已支付，处理退款
            $refundAmount = 0;
            if ($registration->status === EventsRegistrations::STATUS_PAID && $registration->payment) {
                $registration->payment->status = EventsPayments::STATUS_REFUNDED;
                $registration->payment->save();
                $refundAmount = $registration->total_fee;
            }
            
            // 减少赛事项目报名人数
            if ($registration->status === EventsRegistrations::STATUS_PAID) {
                Db::name('marathon_race_options')
                    ->where('id', $registration->race_option_id)
                    ->setDec('registered_count', 1);
            }
            
            Db::commit();
            
            $this->success('取消成功', [
                'refund_amount' => $refundAmount,
                'refund_time' => $refundAmount > 0 ? '3-5个工作日' : ''
            ]);
            
        } catch (Exception $e) {
            Db::rollback();
            $this->error('取消失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取赛事项目列表
     *
     * @ApiTitle    (获取赛事项目列表)
     * @ApiSummary  (根据赛事ID获取可报名的项目列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/events/race_options)
     * @ApiParams   ({"name":"event_id","type":"integer","required":true,"description":"赛事ID"})
     * @ApiReturn   ({"code":0,"msg":"获取成功","data":[{"id":1,"name":"全程马拉松","distance":"42.195km","price":200,"max_participants":1000,"registered_count":500,"remaining":500,"is_available":true,"description":"全程马拉松项目"}]})
     */
    public function race_options()
    {
        $event_id = $this->request->param('event_id');
        
        if (!$event_id) {
            $this->error('缺少赛事ID参数');
        }
        
        $raceOptions = EventsRaceOptions::where('event_id', $event_id)
                                       ->where('status', 'normal')
                                       ->order('sort asc')
                                       ->select();
        
        $result = [];
        foreach ($raceOptions as $option) {
            $result[] = [
                'id' => $option->id,
                'name' => $option->name,
                'distance' => $option->distance,
                'price' => $option->price,
                'max_participants' => $option->max_participants,
                'registered_count' => $option->registered_count,
                'remaining' => $option->remaining,
                'is_available' => $option->hasQuota(),
                'description' => $option->description
            ];
        }
        
        $this->success('获取成功', $result);
    }
    
    /**
     * 获取增值服务列表
     *
     * @ApiTitle    (获取增值服务列表)
     * @ApiSummary  (根据赛事ID获取可购买的增值服务列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/events/addons)
     * @ApiParams   ({"name":"event_id","type":"integer","required":true,"description":"赛事ID"})
     * @ApiReturn   ({"code":0,"msg":"获取成功","data":[{"id":1,"name":"赛事保险","code":"insurance","price":20,"description":"赛事意外保险"}]})
     */
    public function addons()
    {
        $event_id = $this->request->param('event_id');
        
        if (!$event_id) {
            $this->error('缺少赛事ID参数');
        }
        
        $addons = \app\api\model\MarathonAddons::where('event_id', $event_id)
                                              ->where('status', 'active')
                                              ->order('sort asc, id asc')
                                              ->select();
        
        $result = [];
        foreach ($addons as $addon) {
            $result[] = [
                'id' => $addon->id,
                'name' => $addon->name,
                'code' => $addon->type, // 使用type字段作为code
                'key' => $addon->type, // 添加key字段供小程序使用
                'price' => (float)$addon->price,
                'description' => $addon->description
            ];
        }
        
        $this->success('获取成功', $result);
    }
    
    /**
     * 查询报名状态
     *
     * @ApiTitle    (查询报名状态)
     * @ApiSummary  (根据订单号查询报名状态)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/events/check_registration)
     * @ApiParams   ({"name":"order_no","type":"string","required":true,"description":"订单号"})
     * @ApiReturn   ({"code":0,"msg":"获取成功","data":{"status":"paid","status_text":"已支付","registration_id":12345,"qr_code":"data:image/png;base64,..."}})
     */
    public function check_registration()
    {
        $orderNo = $this->request->param('order_no');
        
        if (!$orderNo) {
            $this->error('缺少订单号参数');
        }
        
        $registration = EventsRegistrations::where('order_no', $orderNo)->find();
        if (!$registration) {
            $this->error('报名记录不存在');
        }
        
        $result = [
            'status' => $registration->status,
            'status_text' => $registration->status_text,
            'registration_id' => $registration->id
        ];
        
        // 如果已支付，生成参赛二维码
        if ($registration->status === EventsRegistrations::STATUS_PAID) {
            $result['qr_code'] = $this->generateQRCode($registration->order_no);
        }
        
        $this->success('获取成功', $result);
    }
    
    /**
     * 创建支付订单
     *
     * @ApiTitle    (创建支付订单)
     * @ApiSummary  (根据报名ID创建支付订单)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/events/create_order)
     * @ApiParams   ({"name":"registration_id","type":"integer","required":true,"description":"报名ID"})
     * @ApiParams   ({"name":"payment_method","type":"string","required":true,"description":"支付方式(wechat/balance)"})
     * @ApiReturn   ({"code":0,"msg":"订单创建成功","data":{"order_no":"REG202412250001","total_fee":200.00,"payment_url":"https://api.example.com/pay?order_no=REG202412250001"}})
     */
    public function create_order()
    {
        $registrationId = $this->request->post('registration_id');
        $paymentMethod = $this->request->post('payment_method', 'wechat');
        
        if (!$registrationId) {
            $this->error('缺少报名ID参数');
        }
        
        if (!in_array($paymentMethod, ['wechat', 'balance'])) {
            $this->error('不支持的支付方式');
        }
        
        $registration = EventsRegistrations::find($registrationId);
        if (!$registration) {
            $this->error('报名记录不存在');
        }
        
        if ($registration->status !== EventsRegistrations::STATUS_PENDING) {
            $this->error('报名状态不正确');
        }
        
        // 检查是否已有订单
        $existOrder = EventsPayments::where('order_id', $registrationId)->find();
        if ($existOrder) {
            $this->success('订单已存在', [
                'order_no' => $registration->order_no,
                'total_fee' => $registration->total_fee,
                'payment_url' => '/api/payment/pay?order_no=' . $registration->order_no
            ]);
        }
        
        // 创建支付订单
        $order = new EventsPayments;
        $order->order_id = $registrationId;
        $order->user_id = $this->auth->id;
        $order->amount = $registration->total_fee;
        $order->payment_method = $paymentMethod;
        $order->status = EventsPayments::STATUS_PENDING;
        $order->createtime = time();
        $order->updatetime = time();
        
        if (!$order->save()) {
            $this->error('订单创建失败');
        }
        
        $this->success('订单创建成功', [
            'order_no' => $registration->order_no,
            'total_fee' => $registration->total_fee,
            'payment_url' => '/api/payment/pay?order_no=' . $registration->order_no
        ]);
    }
    
    /**
     * 获取热门赛事
     *
     * @ApiTitle    (获取热门赛事)
     * @ApiSummary  (获取标记为热门的赛事列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/events/hot)
     * @ApiParams   ({"name":"limit","type":"integer","required":false,"description":"返回数量，默认5"})
     * @ApiReturn   ({"code":0,"msg":"获取成功","data":[{"id":1,"name":"2024北京马拉松","date":"2024-10-15","location":"北京市","banner":"/uploads/events/banner1.jpg","countdownDays":30}]})
     */
    public function hot()
    {
        $limit = $this->request->param('limit', 5);
        
        $events = EventsModel::where('status', 'normal')
                            ->where('is_hot', 1)
                            ->order('weigh desc, id desc')
                            ->limit($limit)
                            ->select();
        
        $result = [];
        foreach ($events as $event) {
            $result[] = [
                'id' => $event->id,
                'name' => $event->name,
                'date' => $event->event_date ?: '0000-00-00',
                'location' => $event->location,
                'coverImage' => $event->image ?: '',
                'countdownDays' => $this->calculateCountdownDays($event->event_date),
                'participants' => $event->participants ?: 30000,
                'type' => $event->type ?: 'full'
            ];
        }
        
        $this->success('获取成功', $result);
    }
    
    // 辅助方法
    private function calculateCountdownDays($eventDate)
    {
        $now = time();
        $eventTime = strtotime($eventDate);
        $diff = $eventTime - $now;
        
        if ($diff <= 0) {
            return 0;
        }
        
        return ceil($diff / (24 * 60 * 60));
    }
    
    private function getEventHighlights($event)
    {
        // 从数据库读取赛事亮点
        if (!empty($event->highlights)) {
            $highlights = json_decode($event->highlights, true);
            if (is_array($highlights)) {
                return $highlights;
            }
        }
        
        // 如果数据库中没有数据，返回默认亮点
        return [
            [
                'title' => '专业计时',
                'value' => '采用国际标准计时系统，精确记录每位选手成绩'
            ],
            [
                'title' => '完赛奖牌',
                'value' => '精美设计的完赛奖牌，纪念您的马拉松之旅'
            ],
            [
                'title' => '参赛包',
                'value' => '包含参赛T恤、号码布、计时芯片等必需品'
            ],
            [
                'title' => '赛后补给',
                'value' => '丰富的赛后补给，帮助选手快速恢复体力'
            ]
        ];
    }
    
    private function getEventGallery($eventId)
    {
        // 返回赛事图片库，可以从数据库中获取
        return [
            '/uploads/events/gallery1.jpg',
            '/uploads/events/gallery2.jpg',
            '/uploads/events/gallery3.jpg'
        ];
    }
    
    private function getRaceOptions($eventId)
    {
        $options = EventsRaceOptions::where('event_id', $eventId)
                                   ->where('status', 'in', ['normal', 'active', ''])
                                   ->order('sort asc')
                                   ->select();
        
        $result = [];
        foreach ($options as $option) {
            // 生成key，优先使用name的英文映射，否则使用拼音或简化名称
            $key = $this->generateRaceOptionKey($option->name);
            
            $price = (float)$option->price;

            $result[$key] = [
                'id' => $option->id,
                'name' => $option->name,
                'distance' => $option->distance,
                'price' => $price
            ];
        }
        
        return $result;
    }
    
    /**
     * 生成赛事项目的key
     */
    private function generateRaceOptionKey($name)
    {
        // 常见的马拉松项目名称映射
        $mapping = [
            '全程马拉松' => 'full',
            '全马' => 'full',
            '半程马拉松' => 'half', 
            '半马' => 'half',
            '迷你马拉松' => 'mini',
            '迷你' => 'mini',
            '5公里' => 'mini',
            '10公里' => 'ten_km',
            '健康跑' => 'health_run'
        ];
        
        // 如果有直接映射，使用映射值
        if (isset($mapping[$name])) {
            return $mapping[$name];
        }
        
        // 否则生成简化的key
        $key = strtolower(str_replace(['马拉松', '公里', ' ', '　'], ['', 'km', '_', '_'], $name));
        $key = preg_replace('/[^a-z0-9_]/', '', $key);
        
        return $key ?: 'other';
    }
     
     /**
      * 获取赛事总参与人数
      */
     private function getTotalParticipants($eventId)
     {
         $total = EventsRaceOptions::where('event_id', $eventId)
                                  ->where('status', 'normal')
                                  ->sum('registered');
         
         return (int)$total;
     }
     
     /**
      * 获取赛事起始价格
      */
     private function getStartingPrice($eventId)
     {
         $minPrice = EventsRaceOptions::where('event_id', $eventId)
                                     ->where('status', 'normal')
                                     ->min('price');
         
         return (float)($minPrice ?: 0);
     }
     
     /**
      * 计算倒计时天数
      */
     private function getCountdownDays($eventDate)
     {
         if (!$eventDate) {
             return 0;
         }
         
         $eventTimestamp = is_numeric($eventDate) ? $eventDate : strtotime($eventDate);
         $currentTimestamp = time();
         
         $diffDays = ceil(($eventTimestamp - $currentTimestamp) / (24 * 60 * 60));
         
         return max(0, $diffDays);
     }
     
     private function generateQRCode($orderNo)
    {
        // 生成参赛二维码，这里返回一个示例
        // 实际项目中可以使用二维码生成库
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }
    
    /**
     * 获取赛事增值服务
     */
    private function getEventAddons($eventId)
    {
        $addons = \app\api\model\MarathonAddons::where('event_id', $eventId)
                                              ->where('status', 'active')
                                              ->order('sort asc, id asc')
                                              ->select();
        
        $result = [];
        
        foreach ($addons as $addon) {
            $addonData = [
                'id' => $addon->id,
                'name' => $addon->name,
                'code' => $addon->type, // 使用type字段作为code
                'key' => $addon->type, // 添加key字段供小程序使用
                'price' => (float)$addon->price,
                'description' => $addon->description
            ];

            $result[] = $addonData;
        }
        
        return $result;
    }
}
