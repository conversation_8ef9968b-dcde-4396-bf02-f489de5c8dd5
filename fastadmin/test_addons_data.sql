-- 为赛事ID=2添加增值服务测试数据
INSERT INTO `fa_addons` (`event_id`, `name`, `type`, `price`, `description`, `status`, `sort`, `createtime`, `updatetime`) VALUES
(2, '赛事保险', 'insurance', 20.00, '为参赛者提供赛事期间的意外伤害保险', 'active', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '照片套餐', 'photo', 50.00, '专业摄影师拍摄的个人参赛照片套餐', 'active', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '定制奖牌', 'medal', 80.00, '个性化定制完赛奖牌，刻上您的姓名和成绩', 'active', 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '交通接送', 'transportation', 30.00, '赛事当天往返交通接送服务', 'active', 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
